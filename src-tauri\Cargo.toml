[package]
name = "fish-chat"
version = "0.3.0"
description = "这是一个闲鱼网页 IM 套壳软件，用来提供操作系统的原生通知功能，并且避免把闲鱼挂在浏览器后台。"
authors = ["GOKORURI007"]
edition = "2024"

[lib]
name = "fish_chat_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["tray-icon", "unstable", "image-png"] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
tauri-plugin-notification = "2"
tauri-plugin-store = "2"
tauri-plugin-dialog = "2"
tokio = "1.47.0"
image = "0.25.6"
futures-util = "0.3.31"


[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-global-shortcut = "2"

