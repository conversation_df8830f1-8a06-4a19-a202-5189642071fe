{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006 --no-open", "build-storybook": "storybook build"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "next": "15.4.5", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@chromatic-com/storybook": "4.1.0", "@eslint/eslintrc": "^3", "@storybook/addon-a11y": "9.1.1", "@storybook/addon-docs": "9.1.1", "@storybook/addon-vitest": "9.1.1", "@storybook/nextjs-vite": "^9.1.1", "@storybook/test": "^8.6.14", "@tailwindcss/postcss": "^4", "@types/mdx": "^2.0.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9", "eslint-config-next": "15.4.5", "eslint-plugin-storybook": "9.1.1", "playwright": "^1.54.2", "storybook": "9.1.1", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}