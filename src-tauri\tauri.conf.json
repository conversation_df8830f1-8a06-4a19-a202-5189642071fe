{"$schema": "https://schema.tauri.app/config/2", "productName": "fish-chat", "version": "0.1.0", "identifier": "com.fish-chat.app", "build": {"frontendDist": "../src"}, "app": {"withGlobalTauri": true, "windows": [{"title": "fish-chat", "width": 1200, "height": 720, "minWidth": 1200, "minHeight": 720}], "security": {"dangerousDisableAssetCspModification": true}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico", "icons/icon.png", "icons/icon_dark.png"]}}